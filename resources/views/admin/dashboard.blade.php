<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Social Media Poster</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 1.5rem;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-card h3 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .stat-card .number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .admin-menu {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        
        .menu-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        
        .menu-card:hover {
            transform: translateY(-5px);
        }
        
        .menu-card h3 {
            color: #333;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .menu-card p {
            color: #666;
            margin-bottom: 1rem;
        }
        
        .menu-card .actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s;
            display: inline-block;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        
        .btn-info:hover {
            background: #138496;
        }
        
        .welcome-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .welcome-section h2 {
            color: #333;
            margin-bottom: 1rem;
        }
        
        .welcome-section p {
            color: #666;
            font-size: 1.1rem;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎨 Social Media Poster - Admin Panel</h1>
        <div class="user-info">
            <span>Welcome, {{ Auth::user()->name }}!</span>
            <form method="POST" action="{{ route('admin.logout') }}" style="display: inline;">
                @csrf
                <button type="submit" class="logout-btn">Logout</button>
            </form>
        </div>
    </div>
    
    <div class="container">
        <div class="welcome-section">
            <h2>Welcome to Admin Dashboard</h2>
            <p>Manage your social media poster application from here</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <h3>Total Users</h3>
                <div class="number">{{ \App\Models\User::count() }}</div>
            </div>
            <div class="stat-card">
                <h3>Total Plans</h3>
                <div class="number">{{ \App\Models\Plan::count() }}</div>
            </div>
            <div class="stat-card">
                <h3>Total Posters</h3>
                <div class="number">{{ \App\Models\Poster::count() }}</div>
            </div>
            <div class="stat-card">
                <h3>Total Frames</h3>
                <div class="number">{{ \App\Models\Frame::count() }}</div>
            </div>
        </div>
        
        <div class="admin-menu">
            <div class="menu-card">
                <h3>📋 Plans Management</h3>
                <p>Create and manage subscription plans for users</p>
                <div class="actions">
                    <a href="{{ route('admin.plans.index') }}" class="btn btn-primary">View Plans</a>
                    <a href="{{ route('admin.plans.create') }}" class="btn btn-success">Add New Plan</a>
                </div>
            </div>
            
            <div class="menu-card">
                <h3>🖼️ Posters Management</h3>
                <p>Upload and manage poster templates</p>
                <div class="actions">
                    <a href="{{ route('admin.posters.index') }}" class="btn btn-primary">View Posters</a>
                    <a href="{{ route('admin.posters.create') }}" class="btn btn-success">Add New Poster</a>
                </div>
            </div>
            
            <div class="menu-card">
                <h3>🖼️ Frames Management</h3>
                <p>Upload and manage frame templates</p>
                <div class="actions">
                    <a href="{{ route('admin.frames.index') }}" class="btn btn-primary">View Frames</a>
                    <a href="{{ route('admin.frames.create') }}" class="btn btn-success">Add New Frame</a>
                </div>
            </div>
            
            <div class="menu-card">
                <h3>💰 Transactions</h3>
                <p>View all payment transactions</p>
                <div class="actions">
                    <a href="{{ route('admin.transactions.index') }}" class="btn btn-info">View Transactions</a>
                </div>
            </div>
            
            <div class="menu-card">
                <h3>⚙️ Settings</h3>
                <p>Configure application settings</p>
                <div class="actions">
                    <a href="{{ route('admin.settings.index') }}" class="btn btn-info">App Settings</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
